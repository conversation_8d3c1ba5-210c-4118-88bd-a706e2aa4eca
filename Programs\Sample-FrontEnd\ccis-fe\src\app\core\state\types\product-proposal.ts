import { IAttachments, LoadingResult } from "@interface/common.interface";
import {
  IAffiliation,
  ICooperative,
  ICooperativeOfficer,
  IProductProposal,
  IProductProposalApprovalUpdateStatusPayload,
  IProductProposalNotaryUpdateStatusPayload,
  IProductProposalResponse,
  IProvisionApproval,
} from "@interface/product-proposal.interface";
import { IProduct, IProductRevisions } from "@interface/products.interface";
import { IUser } from "@interface/user.interface";

export type TProductProposalState = {
  step: number | null;
  customType?: string;
  isEditedGuidelines?: boolean;
  cooperative?: ICooperative;
  cooperatives: ICooperative[];
  managementPercentFee: number;
  cdaCoopearative?: ICooperative;
  cdaCooperatives: ICooperative[];
  productProposals: IProductProposal[];
  productProposalProvisions: IProductProposal[];
  productProposalNotarizations: TProductProposalNotary[];
  updatePartnershipAgreement?: TProductProposalNotary;
  proposedProduct?: IProductProposal;
  getProductProposal: TGetProductProposal;
  postProductProposal: LoadingResult;
  putProductProposal: LoadingResult;
  getCooperatives: LoadingResult;
  getCdaCooperatives: LoadingResult;
  getProductProposalNotarization: LoadingResult;
  postProductProposalsNotarizations: LoadingResult;
  getMarketingProductProposals: TGetProductProposal;
  getProductProposalProvisions: LoadingResult;
  signatory?: IProvisionApproval;
};

export type TProductProposalPayload = {
  cooperativeId?: string | number;
  productId?: string;
  managementPercentFee?: string | number;
  proposableId?: string;
  proposableType?: string;
  status?: string;
  proposalType?: string;
  productStatus?: string;
  productRevision?: IProductRevisions;
  attachments?: IAttachments[]; // need to check if this is needed
};

export type TCooperativePayload = {
  id?: number | string;
  coopCode?: string;
  coopName?: string;
  coopAcronym?: string;
  streetAddress?: string;
  barangay?: string;
  city?: string;
  province?: string;
  zipCode?: string;
  emailAddress?: string;
  websiteAddress?: string;
  telephoneNumber?: number | string;
  cdaRegistrationNumber?: number | string;
  cdaRegistrationDate?: string;
  cdaCocNumber?: number | string;
  cdaCocDate?: string;
  taxIdNumber?: number | string;
  taxIdDate?: string;
  taxCteNumber?: number | string;
  taxCteExpiryDate?: string;
  coopBranchesCount?: number;
  coopMembersCount?: number;
  coopMembersMaleCount?: number;
  coopMembersFemaleCount?: number;
  coopTotalAssets?: number;
  status?: string;
  mainBranchId?: number | string;
  cooperativeTypeId?: number | string;
  cooperativeCategoryId?: number | string;
  cooperativeAffiliations?: IAffiliation[];
  cooperativeOfficers?: ICooperativeOfficer[];
  category?: string;
  type?: string;
  createdAt?: string;
  updatedAt?: string;
};

export interface TProductProposalNotary {
  agreementNotarizationStatus: string;
  id: number | string;
  productProposal: IProductProposal;
  cooperative: ICooperative;
  cooperativeOfficers: ICooperativeOfficer;
  product: IProduct;
  proposalNotarization?: IProductProposalNotaryUpdateStatusPayload;
  createdAt?: string;
  updatedAt?: string;
  deletedAt?: number;
}

export type TGetProductProposal = LoadingResult & {
  data?: IProductProposalResponse;
};

export interface TProductProposalApproval {
  id: number | string;
  productProposal?: IProductProposal;
  cooperative?: ICooperative;
  product?: IProduct;
  productProposalApproval?: TProductProposalApprovalUpdateStatusPayload;
}

export interface TProductProposalApprovalUpdateStatusPayload {
  productProposalId: number | string;
  approvalStatus?: string;
  approvalDate?: string;
  approvalRemarks?: string;
}

export interface TProductProposalApprovalUnderwriting {
  id: number | string;
  productProposal: IProductProposal;
  proposable: IProductRevisions;
  cooperative?: ICooperative;
  product?: IProduct;
  productProposalApproval?: IProductProposalApprovalUpdateStatusPayload;
  underwritingStatus?: string;
  claimStatus?: string;
  provisionApproval?: IProvisionApproval[];
  createdAt?: string;
}

export interface TProvisionApproval {
  id: number;
  productProposalId: number;
  userId: number;
  user?: IUser;
  approvalType: string;
  status: string;
  remarks?: string | null;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string | null;
}
