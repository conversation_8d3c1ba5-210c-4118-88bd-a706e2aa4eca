import { LoadingResult } from "@interface/common.interface";
import { PayloadAction } from "@reduxjs/toolkit";

// Defining the state type for profile management
export type TProfileState = {
  profile: TUserProfile; // State to set user record
  profilePercent?: string | number;
  updateProfile: LoadingResult;
  changePassword: LoadingResult;
  uploadUserProfilePicture: LoadingResult;
  uploadUserSignature: LoadingResult;
};

export type TUserProfile = {
  firstname: string;
  middlename: string;
  lastname: string;
  gender: string;
  address: string;
  contactNumber: string;
  birthDate: string;
  email: string;
  companyId: string;
  positionId: string;
  departmentId: string;
  profilePicturePath?: string;
  signaturePath?: string;
};

export type TChangePassword = {
  currentPassword: string;
  newPassword: string;
};

export type TFileToUpload = {
  [key: string]: Array<File>;
};

export type TUserProfilePayloadAction = PayloadAction<TUserProfile>;
export type TChangePasswordPayloadAction = PayloadAction<TChangePassword>;
export type TFileToUploadPayloadAction = PayloadAction<TFileToUpload>;
