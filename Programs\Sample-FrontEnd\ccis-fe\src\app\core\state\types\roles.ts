import { IRole } from "@interface/roles.interface"
import { LoadingResult } from "@interface/common.interface";
import { PayloadAction } from "@reduxjs/toolkit";

export type TRolesState = {
    role?:IRole;
    roles?: IRole[];
    getRoles: LoadingResult;
    postRole: LoadingResult;
    putRole: LoadingResult;
    destroyRole: LoadingResult;
}

export type TRolesPayload = {
    id: number,
    data: IRole;        
}

export type TRolesIDPayload = {
    filter?:number | string;
    id?: number | string;

}
export type TRolesPayloadAction = PayloadAction<TRolesPayload & { filter?: string }>;

export type TRolesActionPayload = PayloadAction<{ data?: IRole[] }>;
export type TRolesActionPayloadIrole = PayloadAction<IRole>;

export type TRolesIDPayloadActionPayload = PayloadAction<TRolesIDPayload>;
export type TGetRolesWithFilterActionPayload = PayloadAction<{ filter: string, id?:number}>