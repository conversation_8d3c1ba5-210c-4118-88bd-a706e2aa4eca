import { PayloadAction } from "@reduxjs/toolkit";
import { LoadingResult } from ".";
import {
  IProduct,
  IProductResponse,
  IProductRevisions,
  ISignatories,
  IAttachment,
} from "@interface/products.interface";
import { IGuideline } from "@interface/guidelines.interface";
import { ICommissionStructure } from "@interface/commission-structure.interface";

export type TProductState = {
  compliance?: TCompliance;
  forCompliance?: boolean;
  products: IProduct[];
  mainProducts: IProduct[];
  product?: IProduct;
  productActivity?: TProductActivity[];
  productWithRevisions?: IProduct;
  productRevisions?: IProductRevisions[];
  revisionDetails?: IProductRevisions & { product: IProduct };
  signatory?: ISignatories;
  complianceReviewAttachment?: IAttachment[];
  surveyResultAttachment?: IAttachment[];
  getProducts: GetProducts;
  getMainProducts: GetProducts;
  getProduct: LoadingResult;
  postProduct: LoadingResult;
  putProduct: LoadingResult;
  postCompleteProduct: LoadingResult;
  postProductRevision: LoadingResult;
  putProductRevision: LoadingResult;
  getProductActivity: LoadingResult;
  getProductRevisions: LoadingResult;
  getRevisionDetails: LoadingResult;
};

export type TProductPayload = {
  id?: string | number;
  name?: string;
  productCode?: string;
  description?: string;
  approvalType?: string;
  approvalStatus?: string;
  status?: string;
  productTypeId?: number | string | null;
  productCategoryId?: number | string | null;
  targetMarketId?: number | string | null;
  parentId?: number | string | null;
};

export type TProductRevisionPayload = {
  id?: string | number;
  revisionNumber?: string;
  productId?: string | number;
  description?: string;
  approvalType?: string;
  approvalStatus?: string;
  productGuidelines?: IGuideline[];
  signatories?: ISignatories[];
  commission?: ICommissionStructure;
};

export type TCompliance = {
  id?: string | number;
  name?: string;
};

export type TProductActivity = {
  id: string | number;
  description: string;
  created_at: string;
};

export type TRevisionsWithProduct = IProductRevisions & { product: IProduct };

export type TProductActionPayloadAction = PayloadAction<TProductPayload>;
export type TProductRevisionPayloadAction =
  PayloadAction<TProductRevisionPayload>;

export type GetProducts = LoadingResult & {
  data?: IProductResponse;
};
