import { IQuotation, QuotationResponse } from "@interface/quotation.interface";
import { LoadingResult } from ".";
import { PayloadAction } from "@reduxjs/toolkit";

export type TQuotationState = {
  quotations: IQuotation[];
  quotation?: IQuotation;
  getQuotation: GetQuotation;
  calculateQuotation: LoadingResult;
  createQuotationWithAer: GetCreatedQuotation;
  createClppQuotationWithAer: GetClppCreatedQuotation;
  createClspQuotationWithAer: GetClspCreatedQuotation;
  getQuotations: GetQuotations;
  getQuotaionsGyrt: GetQuotationsGyrt;
  updateAerStatus: TUpdateAerStatus;
  getAerByQuotationId: TGetAerByQuotationId;
};

export type GetQuotation = LoadingResult & {
  data?: QuotationResponse;
};

export type GetCreatedQuotation = LoadingResult & {
  data?: TCreateQuotationWithAerResponse;
};

export type GetClppCreatedQuotation = LoadingResult & {
  data?: TCreateClppQuotationWithAerResponse;
};

export type GetClspCreatedQuotation = LoadingResult & {
  data?: TCreateClspQuotationWithAerResponse;
};

export type TGyrtCalculationPayload = {
  averageAge: number;
  averageClaims: number;
  groupSize: number;
  gyrtBenefits: { benefitId: number; coverage: number; memberType: string }[];
  quotationCommissionDistribution: { commissionTypeId: number; rate: number }[];
};
export type TDemographicData = {
  fileName: string;
  productType: string;
};
export type TCalculateGYRTPremiumPayloadAction =
  PayloadAction<TGyrtCalculationPayload>;

export type GetQuotationsGyrt = LoadingResult & {
  data?: QuotationResponse;
};

export type GetQuotations = LoadingResult & {
  data?: QuotationResponse;
};

export type TGetAerByQuotationId = LoadingResult & {
  data?: TAerResponse[];
};

export type TUpdateAerStatus = LoadingResult & {
  data?: {
    id: number;
    status: string;
    remarks: string;
  };
};

export type GetAerByQuotationId = LoadingResult & {
  data?: {
    id: number;
    status: string;
  };
};
export type TAerResponse = {
  id: number;
  productId: number;
  quotationId: number;
  options: string;
  remarks: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  quotation: {
    id: number;
    coopId: number;
    previousProvider: string;
    branch: string | null;
    contestability: number;
    totalNumberOfMembers: number;
    productId: number | null;
    coverageTypeId: number | null;
    createdAt: string;
    updatedAt: string;
    deletedAt: string | null;
    gyrtBenefits: any[]; // Could be typed more specifically if needed
  };
};

export type TGetAerByQuotationIdPayloadAction = PayloadAction<string>;

export type TUpdateAerStatusPayload = {
  id: string | number;
  remarks?: string;
  status: string;
};

export type TUpdateAerStatusPayloadAction =
  PayloadAction<TUpdateAerStatusPayload>;

export type TCreateQuotationWithAerPayload = {
  status: string;
  premiumBudget: number;
  averageAge: number;
  averageClaims: number;
  quotations: {
    coopId: number;
    previousProvider: string;
    // branch: string;
    contestability: number;
    totalNumberOfMembers: number;
  };
  quotationCondition: {
    condition: string;
  };
  gyrtAges: {
    ageType: string;
    minimum: number;
    maximum: number;
    exitAge: number;
  }[];
  claimsExperienceYears: {
    year: string;
    numberOfDeaths: number;
    totalClaimAmount: number;
  }[];
  claimsExperienceAges: {
    ageFrom: number;
    ageTo: number;
    numberOfDeaths: number;
    totalClaimAmount: number;
  }[];
  commissionDistributions: {
    commissionTypeId: number;
    ageTypeId: number;
    ageFrom?: number;
    ageTo?: number;
    rate: number;
  }[];
  options: {
    gyrtBenefits: {
      benefitId: number;
      coverage: number;
      option: number;
    }[];
    quotationPremiums: {
      grossPremium: number;
      netPremium: number;
      option: number;
    }[];
    aerOptions: string;
  };
};

export type TCreateClppQuotationWithAerPayload = {
  quotations: {
    coopId: number | string;
    previousProvider: string;
    // branch: string;
    contestability: number | string;
    totalNumberOfMembers: number;
    coverageTypeId: number;
  };
  quotationCondition: {
    condition: string;
  };
  claimsExperienceYears: {
    year: string | number;
    numberOfDeaths: number;
    totalClaimAmount: number;
  }[];
  claimsExperienceAges: {
    ageFrom: number;
    ageTo: number;
    numberOfDeaths: number;
    totalClaimAmount: number;
  }[];
  commissionDistributions: {
    commissionTypeId: number;
    rate: number;
    commissionAgeType: number;
    ageFrom?: number;
    ageTo?: number;
  }[];
  options: {
    clppBenefits: {
      ageFrom: number;
      ageTo: number;
      benefitId: number;
      maximumCoverage: number;
      maximumTerm: number;
      nml: number;
      nel: number;
      option: number | string;
    }[];
    quotationPremiums: {
      grossPremium: number;
      netPremium: number;
      option: number | string;
    }[];
    aerOptions: string;
  };
  loanPortfolioAges: {
    ageFrom: number;
    ageTo: number;
    totalNumberOfMembers: number;
    totalLoanPortfolioAmount: number;
  }[];
  loanPortfolioYears: {
    year: number;
    minimumAge: number;
    maximumAge: number;
    minimumLoanAmount: number;
    maximumLoanAmount: number;
    totalNumberOfBorrowers: number;
    totalLoanPortfolio: number;
  }[];
};

export type TCreateClspQuotationWithAerPayload = {
  id?: string | number;
  status?: string;
  coverageBasis: string;
  averageAge: number;
  quotations: {
    coopId: number; // found in coop table
    previousProvider: string;
    branch: string;
    contestability: number; // found in contestability table
    totalNumberOfMembers: number;
  };
  quotationCondition: {
    condition: string;
  };
  claimsExperienceYears: {
    year: string;
    numberOfDeaths: number;
    totalClaimAmount: number;
  }[];
  claimsExperienceAges: {
    ageFrom: number;
    ageTo: number;
    numberOfDeaths: number;
    totalClaimAmount: number;
  }[];
  commissionDistributions: {
    commissionTypeId: number;
    rate: number;
  }[];
  clspPortfolioAges: {
    ageFrom: number;
    ageTo: number;
    totalNumberOfMembers: number;
    totalCapital: number;
    portfolioType: string;
  }[];
  clspPortfolioYears: {
    year: string;
    minimumAmount: number;
    maximumAmount: number;
    totalPortfolio: number;
    portfolioType: string;
  }[];
  options: {
    clspBenefits: {
      benefitId: number;
      ageFrom: number;
      ageTo: number;
      maximumCoverageAmount: number;
      rate: number;
      option: number;
    }[];
    aerOptions: string; // JSON string, e.g., "[1,2]"
  };
};

export interface TCreateQuotationWithAerResponse {
  id: number;
  premiumBudget: string;
  averageAge: number;
  averageClaims: number;
  maxDependents: number;
  quotationId: number;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  quotations: {
    id: number;
    coopId: number;
    previousProvider: string;
    branch: string;
    contestability: number;
    totalNumberOfMembers: number;
    productId: number;
    coverageTypeId: number | null;
    createdAt: string;
    updatedAt: string;
    deletedAt: string | null;
    quotationClaimsExperienceAge: {
      id: number;
      ageFrom: number;
      ageTo: number;
      numberOfDeaths: number;
      totalClaimAmount: string;
      quotationId: number;
      createdAt: string;
      updatedAt: string;
      deletedAt: string | null;
    }[];
    quotationClaimsExperienceYear: {
      id: number;
      year: string;
      numberOfDeaths: number;
      totalClaimAmount: string;
      quotationId: number;
      createdAt: string;
      updatedAt: string;
      deletedAt: string | null;
    }[];
    quotationCommissionDistribution: {
      id: number;
      commissionTypeId: number;
      rate: string;
      quotationId: number;
      createdAt: string;
      updatedAt: string;
      deletedAt: string | null;
      isAgentInput: number;
      commissionAgeType: string | null;
      ageFrom: number | null;
      ageTo: number | null;
    }[];
    quotationPremium: {
      id: number;
      quotationId: number;
      grossPremium: string;
      netPremium: string;
      option: number;
      createdAt: string;
      updatedAt: string;
      deletedAt: string | null;
    }[];
    quotationCondition: {
      id: number;
      quotationId: number;
      condition: string;
      createdAt: string;
      updatedAt: string;
      deletedAt: string | null;
    };
  };
  gyrtAges: any[];
  gyrtBenefits: any[];
  status: string;
}

export interface TCreateClppQuotationWithAerResponse {
  id: number;
  premiumBudget: string;
  averageAge: number;
  averageClaims: number;
  maxDependents: number;
  quotationId: number;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  quotations: {
    id: number;
    coopId: number;
    previousProvider: string;
    branch: string;
    contestability: number;
    totalNumberOfMembers: number;
    productId: number;
    coverageTypeId: number | null;
    createdAt: string;
    updatedAt: string;
    deletedAt: string | null;
    quotationClaimsExperienceAge: {
      id: number;
      ageFrom: number;
      ageTo: number;
      numberOfDeaths: number;
      totalClaimAmount: string;
      quotationId: number;
      createdAt: string;
      updatedAt: string;
      deletedAt: string | null;
    }[];
    quotationClaimsExperienceYear: {
      id: number;
      year: string;
      numberOfDeaths: number;
      totalClaimAmount: string;
      quotationId: number;
      createdAt: string;
      updatedAt: string;
      deletedAt: string | null;
    }[];
    quotationCommissionDistribution: {
      id: number;
      commissionTypeId: number;
      rate: string;
      quotationId: number;
      createdAt: string;
      updatedAt: string;
      deletedAt: string | null;
      isAgentInput: number;
      commissionAgeType: string | null;
      ageFrom: number | null;
      ageTo: number | null;
    }[];
    quotationPremium: {
      id: number;
      quotationId: number;
      grossPremium: string;
      netPremium: string;
      option: number;
      createdAt: string;
      updatedAt: string;
      deletedAt: string | null;
    }[];
    quotationCondition: {
      id: number;
      quotationId: number;
      condition: string;
      createdAt: string;
      updatedAt: string;
      deletedAt: string | null;
    };
  };
}

export interface TCreateClspQuotationWithAerResponse {
  id: number;
  premiumBudget: string;
  averageAge: number;
  averageClaims: number;
  maxDependents: number;
  quotationId: number;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  quotations: {
    id: number;
    coopId: number;
    previousProvider: string;
    branch: string;
    contestability: number;
    totalNumberOfMembers: number;
    productId: number;
    coverageTypeId: number | null;
    createdAt: string;
    updatedAt: string;
    deletedAt: string | null;
    quotationClaimsExperienceAge: {
      id: number;
      ageFrom: number;
      ageTo: number;
      numberOfDeaths: number;
      totalClaimAmount: string;
      quotationId: number;
      createdAt: string;
      updatedAt: string;
      deletedAt: string | null;
    }[];
    quotationClaimsExperienceYear: {
      id: number;
      year: string;
      numberOfDeaths: number;
      totalClaimAmount: string;
      quotationId: number;
      createdAt: string;
      updatedAt: string;
      deletedAt: string | null;
    }[];
    quotationCommissionDistribution: {
      id: number;
      commissionTypeId: number;
      rate: string;
      quotationId: number;
      createdAt: string;
      updatedAt: string;
      deletedAt: string | null;
      isAgentInput: number;
      commissionAgeType: string | null;
      ageFrom: number | null;
      ageTo: number | null;
    }[];
    quotationPremium: {
      id: number;
      quotationId: number;
      grossPremium: string;
      netPremium: string;
      option: number;
      createdAt: string;
      updatedAt: string;
      deletedAt: string | null;
    }[];
    quotationCondition: {
      id: number;
      quotationId: number;
      condition: string;
      createdAt: string;
      updatedAt: string;
      deletedAt: string | null;
    };
  };
}

export type TCreateQuotationWithAerPayloadAction =
  PayloadAction<TCreateQuotationWithAerPayload>;

export type TCreateClppQuotationWithAerPayloadAction =
  PayloadAction<TCreateClppQuotationWithAerPayload>;

export type TCreateClspQuotationWithAerPayloadAction =
  PayloadAction<TCreateClspQuotationWithAerPayload>;
